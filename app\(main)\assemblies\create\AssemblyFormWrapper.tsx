'use client';

import React from 'react';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import AssemblyFormContent from '../[id]/edit/AssemblyFormContent';

/**
 * Wrapper component for the create assembly form that provides the context
 */
export default function AssemblyFormWrapper() {
  return (
    <AssemblyFormProvider>
      <AssemblyFormContent />
    </AssemblyFormProvider>
  );
}

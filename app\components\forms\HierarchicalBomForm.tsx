'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Plus, Trash2, ChevronDown, ChevronRight, Package, Layers } from 'lucide-react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Badge } from '@/app/components/data-display/badge';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/app/components/forms/Form';
import { ProductFormData } from './ProductFormSchema';
import { cn } from '@/app/lib/utils';

/**
 * Props for HierarchicalBomForm component
 */
interface HierarchicalBomFormProps {
  /**
   * Name of the field in the parent form
   */
  name: string;
  
  /**
   * Maximum nesting depth allowed
   */
  maxDepth?: number;
  
  /**
   * Available assemblies for selection
   */
  assemblies?: Array<{
    _id: string;
    name: string;
    assemblyCode: string;
  }>;
  
  /**
   * Whether the form is disabled
   */
  disabled?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Props for individual BOM component item
 */
interface BomComponentItemProps {
  /**
   * Field name prefix for this component
   */
  fieldName: string;
  
  /**
   * Current nesting level
   */
  level: number;
  
  /**
   * Maximum allowed depth
   */
  maxDepth: number;
  
  /**
   * Index of this component in the parent array
   */
  index: number;
  
  /**
   * Function to remove this component
   */
  onRemove: () => void;
  
  /**
   * Available assemblies
   */
  assemblies: Array<{
    _id: string;
    name: string;
    assemblyCode: string;
  }>;
  
  /**
   * Whether the form is disabled
   */
  disabled?: boolean;
}

/**
 * Individual BOM component item with recursive children support
 * Optimized with React.memo for better performance
 */
const BomComponentItem = memo(function BomComponentItem({
  fieldName,
  level,
  maxDepth,
  index,
  onRemove,
  assemblies,
  disabled = false
}: BomComponentItemProps) {
  const { control, watch } = useFormContext<ProductFormData>();
  const [isExpanded, setIsExpanded] = useState(true);
  
  // Watch the current component data
  const componentData = watch(`${fieldName}` as any);
  
  // Field array for children
  const {
    fields: childFields,
    append: appendChild,
    remove: removeChild
  } = useFieldArray({
    control,
    name: `${fieldName}.children` as any
  });

  const handleAddChild = useCallback(() => {
    if (level < maxDepth) {
      appendChild({
        assemblyId: '',
        quantityRequired: 1,
        children: []
      });
    }
  }, [appendChild, level, maxDepth]);

  const handleRemoveChild = useCallback((childIndex: number) => {
    removeChild(childIndex);
  }, [removeChild]);

  const selectedAssembly = useMemo(() => {
    return assemblies.find(assembly => assembly._id === componentData?.assemblyId);
  }, [assemblies, componentData?.assemblyId]);

  // Dynamic border color based on level using Tailwind classes
  const borderColorClass = {
    0: "border-l-blue-500",
    1: "border-l-emerald-500",
    2: "border-l-amber-500",
    3: "border-l-red-500",
    4: "border-l-purple-500",
    5: "border-l-pink-500"
  }[level] || "border-l-gray-300";

  return (
    <Card className={cn(
      "border-l-4 transition-all duration-200 hover:shadow-md",
      borderColorClass,
      level > 0 && "ml-4 mt-2"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {childFields.length > 0 && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0 hover:bg-muted"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}

            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm">
                Component {index + 1}
                {selectedAssembly && (
                  <span className="ml-2 font-normal text-muted-foreground">
                    ({selectedAssembly.assemblyCode})
                  </span>
                )}
              </CardTitle>
            </div>

            <Badge variant="outline" className="text-xs">
              Level {level + 1}
            </Badge>
          </div>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemove}
            disabled={disabled}
            className="h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Assembly Selection */}
          <FormField
            control={control}
            name={`${fieldName}.assemblyId` as any}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Assembly</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  disabled={disabled}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select assembly..." />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {assemblies.map((assembly) => (
                      <SelectItem key={assembly._id} value={assembly._id}>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          <span>{assembly.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {assembly.assemblyCode}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Quantity Required */}
          <FormField
            control={control}
            name={`${fieldName}.quantityRequired` as any}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity Required</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0.001"
                    step="0.001"
                    placeholder="1"
                    disabled={disabled}
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Children Components */}
        {isExpanded && childFields.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-muted-foreground">
                Sub-components ({childFields.length})
              </h4>
            </div>
            
            <div className="space-y-2">
              {childFields.map((childField, childIndex) => (
                <BomComponentItem
                  key={childField.id}
                  fieldName={`${fieldName}.children.${childIndex}`}
                  level={level + 1}
                  maxDepth={maxDepth}
                  index={childIndex}
                  onRemove={() => handleRemoveChild(childIndex)}
                  assemblies={assemblies}
                  disabled={disabled}
                />
              ))}
            </div>
          </div>
        )}
        
        {/* Add Child Button */}
        {isExpanded && level < maxDepth && (
          <div className="pt-2 border-t">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddChild}
              disabled={disabled}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Sub-component
            </Button>
          </div>
        )}
        
        {level >= maxDepth && (
          <div className="text-xs text-muted-foreground text-center py-2">
            Maximum nesting depth reached
          </div>
        )}
      </CardContent>
    </Card>
  );
});

/**
 * Hierarchical BOM Form Component
 * Allows editing of nested BOM structure for products
 * Optimized with React.memo for better performance
 */
export const HierarchicalBomForm = memo(function HierarchicalBomForm({
  name,
  maxDepth = 5,
  assemblies = [],
  disabled = false,
  className
}: HierarchicalBomFormProps) {
  const { control } = useFormContext<ProductFormData>();
  
  const {
    fields,
    append,
    remove
  } = useFieldArray({
    control,
    name: name as any
  });

  const handleAddComponent = useCallback(() => {
    append({
      assemblyId: '',
      quantityRequired: 1,
      children: []
    });
  }, [append]);

  const handleRemoveComponent = useCallback((index: number) => {
    remove(index);
  }, [remove]);

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Bill of Materials</h3>
          <p className="text-sm text-muted-foreground">
            Define the hierarchical structure of assemblies required for this product
          </p>
        </div>
        
        <Button
          type="button"
          variant="outline"
          onClick={handleAddComponent}
          disabled={disabled}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Component
        </Button>
      </div>
      
      {fields.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Package className="h-12 w-12 text-muted-foreground/50 mb-4" />
            <h4 className="text-lg font-medium text-muted-foreground mb-2">
              No components defined
            </h4>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Add assemblies to define the bill of materials for this product
            </p>
            <Button
              type="button"
              variant="outline"
              onClick={handleAddComponent}
              disabled={disabled}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add First Component
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {fields.map((field, index) => (
            <BomComponentItem
              key={field.id}
              fieldName={`${name}.${index}`}
              level={0}
              maxDepth={maxDepth}
              index={index}
              onRemove={() => handleRemoveComponent(index)}
              assemblies={assemblies}
              disabled={disabled}
            />
          ))}
        </div>
      )}
      
      {fields.length > 0 && (
        <div className="text-xs text-muted-foreground">
          Total components: {fields.length} • Maximum depth: {maxDepth} levels
        </div>
      )}
    </div>
  );
});

export default HierarchicalBomForm;

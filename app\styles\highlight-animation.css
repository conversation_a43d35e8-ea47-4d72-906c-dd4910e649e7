/* Highlight animation for new parts */
@keyframes highlightPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(250, 204, 21, 0.7);
    background-color: rgba(250, 204, 21, 0.1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(250, 204, 21, 0);
    background-color: rgba(250, 204, 21, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(250, 204, 21, 0);
    background-color: transparent;
  }
}

.highlight-new-part {
  animation: highlightPulse 2s ease-out;
}

/* Smooth transitions for form elements */
.form-transition {
  transition: all 0.3s ease-in-out;
}

.form-transition:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Fade in animation for new elements */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Fade out animation for removed elements */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.fade-out {
  animation: fadeOut 0.3s ease-out forwards;
}

"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { ContainerProps } from './types';
import { cn } from '@/app/lib/utils';

/**
 * Client-side component for Container with animations
 */
const ContainerClient: React.FC<ContainerProps> = ({
  children,
  maxWidth = 'xl',
  padding = 'px-4 py-6 md:px-6 lg:px-8',
  className = '',
  centered = true
}) => {
  // Map maxWidth value to appropriate Tailwind class
  const maxWidthClass = maxWidth === 'none' 
    ? '' 
    : maxWidth === 'full'
      ? 'w-full'
      : `max-w-${maxWidth}`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        maxWidthClass,
        padding,
        centered && 'mx-auto',
        className
      )}
    >
      {children}
    </motion.div>
  );
};

export default ContainerClient; 
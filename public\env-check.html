<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Environment Variables Check</title>
  <script src="./config.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2a6496;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .success {
      color: #2c7a2c;
      background-color: #e6ffe6;
    }
    .error {
      color: #c62828;
      background-color: #ffeded;
    }
    .warning {
      color: #ff8c00;
      background-color: #fff7e6;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow: auto;
    }
    button {
      background-color: #2a6496;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #1d4b70;
    }
    input {
      width: 100%;
      padding: 8px;
      margin: 8px 0;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .input-group {
      margin-bottom: 15px;
    }
    label {
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Environment Variables Check</h1>

  <div class="card" id="status-card">
    <h2>Status</h2>
    <p id="status-message">Checking environment variables...</p>
  </div>

  <div class="card">
    <h2>Environment Variables</h2>
    <div class="input-group">
      <label for="mongodb-uri">MONGODB_URI</label>
      <input type="text" id="mongodb-uri" placeholder="Your MongoDB Connection URI">
    </div>
    <div class="input-group">
      <label for="mongodb-db-name">MONGODB_DB_NAME</label>
      <input type="text" id="mongodb-db-name" placeholder="Your MongoDB Database Name">
    </div>
    <button id="save-btn">Save Configuration</button>
  </div>

  <div class="card">
    <h2>Current Environment Configuration</h2>
    <pre id="env-config"></pre>
  </div>

  <div class="card">
    <h2>Next Steps</h2>
    <p>Once you've set up your environment variables:</p>
    <ol>
      <li>Make sure they are correctly configured in your <code>.env</code> file.</li>
      <li>Restart your development server (if you're in development mode).</li>
      <li>If you're in production, redeploy your application.</li>
    </ol>
    <a href="/"><button>Go to Application</button></a>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const statusCard = document.getElementById('status-card');
      const statusMessage = document.getElementById('status-message');
      const envConfig = document.getElementById('env-config');
      const mongodbUriInput = document.getElementById('mongodb-uri');
      const mongodbDbNameInput = document.getElementById('mongodb-db-name');
      const saveBtn = document.getElementById('save-btn');

      function checkEnvironment() {
        try {
          // Check if environment variables are defined
          const env = window.checkEnv ? window.checkEnv() : { hasUrl: false, hasKey: false };

          if (env.isConfigured) {
            statusCard.className = 'card success';
            statusMessage.textContent = '✅ Environment variables are correctly configured.';
          } else {
            const missing = [];
            if (!env.hasMongoDB) missing.push('MONGODB_URI');
            if (!env.hasDBName) missing.push('MONGODB_DB_NAME');

            statusCard.className = 'card error';
            statusMessage.textContent = `❌ Missing environment variables: ${missing.join(', ')}`;
          }

          // Display current config (safely)
          const uri = window.ENV.MONGODB_URI || '(Not set)';
          const dbName = window.ENV.MONGODB_DB_NAME || '(Not set)';

          envConfig.textContent = JSON.stringify({
            MONGODB_URI: uri.includes('mongodb+srv') ? uri.split('@')[0] + '@***' : '(Set, but hidden for security)',
            MONGODB_DB_NAME: dbName
          }, null, 2);

        } catch (error) {
          statusCard.className = 'card error';
          statusMessage.textContent = `❌ Error checking environment: ${error.message}`;
        }
      }

      // Save button handler
      saveBtn.addEventListener('click', function() {
        const uri = mongodbUriInput.value.trim();
        const dbName = mongodbDbNameInput.value.trim();

        if (uri) window.ENV.MONGODB_URI = uri;
        if (dbName) window.ENV.MONGODB_DB_NAME = dbName;

        // Save to localStorage for persistence
        if (uri || dbName) {
          try {
            localStorage.setItem('env_config', JSON.stringify({
              MONGODB_URI: uri || window.ENV.MONGODB_URI,
              MONGODB_DB_NAME: dbName || window.ENV.MONGODB_DB_NAME
            }));

            statusCard.className = 'card success';
            statusMessage.textContent = '✅ Configuration saved successfully! Reload the application to apply changes.';
          } catch (error) {
            statusCard.className = 'card error';
            statusMessage.textContent = `❌ Error saving configuration: ${error.message}`;
          }
        }

        // Update display
        checkEnvironment();
      });

      // Load saved configuration from localStorage
      try {
        const savedConfig = localStorage.getItem('env_config');
        if (savedConfig) {
          const config = JSON.parse(savedConfig);
          window.ENV.MONGODB_URI = config.MONGODB_URI || window.ENV.MONGODB_URI;
          window.ENV.MONGODB_DB_NAME = config.MONGODB_DB_NAME || window.ENV.MONGODB_DB_NAME;

          // Update inputs
          mongodbUriInput.value = window.ENV.MONGODB_URI;
          mongodbDbNameInput.value = window.ENV.MONGODB_DB_NAME;
        }
      } catch (error) {
        console.error('Error loading saved configuration:', error);
      }

      // Initial check
      checkEnvironment();
    });
  </script>
</body>
</html>
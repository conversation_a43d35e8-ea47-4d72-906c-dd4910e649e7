import React from 'react';

/**
 * Interface for navigation items in the sidebar
 */
export interface NavItem {
  /** Icon component to display */
  icon: React.ReactNode;
  /** URL path for the navigation link */
  path: string;
  /** Display label for the navigation item */
  label: string;
  /** Optional badge count to display */
  badge?: number;
  /** Optional category for grouping navigation items */
  category?: string;
}

/**
 * Props for the SidebarClient component
 */
export interface SidebarProps {
  /** Categorized navigation items */
  navItems?: NavItem[];
  /** Initial expanded state */
  initialExpanded?: boolean;
}

/**
 * Type for theme options
 */
export type Theme = 'light' | 'dark'; 
/**
 * Props for the FormContainer component
 */
export interface FormContainerProps {
  /** The form title displayed in the card header */
  title: string;
  /** Optional description displayed below the title */
  description?: string;
  /** Whether the form is currently submitting/loading */
  isLoading?: boolean;
  /** Form-level error message to display */
  error?: string | null;
  /** Whether to animate the container when it mounts */
  animate?: boolean;
  /** CSS class to apply to the container */
  className?: string;
  /** Content to display in the footer section */
  footer?: React.ReactNode;
  /** Max width class for the form container - defaults to max-w-3xl */
  maxWidth?: string;
  /** Children content (the form itself) */
  children: React.ReactNode;
} 
// This is a Server Component by default (no "use client" directive)
import React from 'react';
import dynamic from 'next/dynamic';
import { SidebarProps } from './types';

// Use dynamic import to load the client component with SSR disabled
// This ensures the component only renders on the client, avoiding hydration issues
const SidebarClient = dynamic(() => import('./SidebarClient'), {
  ssr: false,
});

/**
 * Sidebar component for main navigation
 * Server component wrapper that delegates rendering to the client component
 * 
 * @param props - Props to pass to the client component
 */
const Sidebar: React.FC<SidebarProps> = (props) => {
  return <SidebarClient {...props} />;
};

export default Sidebar; 
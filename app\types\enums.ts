/**
 * Enums for Trend IMS
 * 
 * This file contains centralized enum definitions used throughout the application
 * to ensure consistency and type safety.
 */

// ============================================================================
// Assembly Status Enums
// ============================================================================

/**
 * Assembly status enumeration
 */
export const ASSEMBLY_STATUS = {
  ACTIVE: 'active',
  PENDING_REVIEW: 'pending_review',
  DESIGN_PHASE: 'design_phase',
  DESIGN_COMPLETE: 'design_complete',
  OBSOLETE: 'obsolete',
  ARCHIVED: 'archived'
} as const;

/**
 * Array of assembly status values for validation
 */
export const ASSEMBLY_STATUS_VALUES = Object.values(ASSEMBLY_STATUS);

/**
 * Assembly status type
 */
export type AssemblyStatus = typeof ASSEMBLY_STATUS[keyof typeof ASSEMBLY_STATUS];

// ============================================================================
// Batch Status Enums
// ============================================================================

/**
 * Batch status enumeration
 */
export const BATCH_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  PAUSED: 'paused',
  CANCELLED: 'cancelled'
} as const;

/**
 * Array of batch status values for validation
 */
export const BATCH_STATUS_VALUES = Object.values(BATCH_STATUS);

/**
 * Batch status type
 */
export type BatchStatus = typeof BATCH_STATUS[keyof typeof BATCH_STATUS];

// ============================================================================
// Work Order Status Enums
// ============================================================================

/**
 * Work order status enumeration
 */
export const WORK_ORDER_STATUS = {
  PLANNED: 'planned',
  RELEASED: 'released',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  ON_HOLD: 'on_hold',
  CANCELLED: 'cancelled',
  CLOSED: 'closed',
  PENDING: 'pending' // Additional status used in forms
} as const;

/**
 * Array of work order status values for validation
 */
export const WORK_ORDER_STATUS_VALUES = Object.values(WORK_ORDER_STATUS);

/**
 * Work order status type
 */
export type WorkOrderStatus = typeof WORK_ORDER_STATUS[keyof typeof WORK_ORDER_STATUS];

// ============================================================================
// Priority Enums
// ============================================================================

/**
 * Priority level enumeration
 */
export const PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
} as const;

/**
 * Array of priority values for validation
 */
export const PRIORITY_VALUES = Object.values(PRIORITY);

/**
 * Priority type
 */
export type Priority = typeof PRIORITY[keyof typeof PRIORITY];

// ============================================================================
// Part and Inventory Status Enums
// ============================================================================

/**
 * Part status enumeration
 */
export const PART_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  OBSOLETE: 'obsolete'
} as const;

/**
 * Array of part status values for validation
 */
export const PART_STATUS_VALUES = Object.values(PART_STATUS);

/**
 * Part status type
 */
export type PartStatus = typeof PART_STATUS[keyof typeof PART_STATUS];

/**
 * Stock status enumeration
 */
export const STOCK_STATUS = {
  AVAILABLE: 'available',
  LOW: 'low',
  UNAVAILABLE: 'unavailable',
  IN_STOCK: 'in_stock',
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock'
} as const;

/**
 * Array of stock status values for validation
 */
export const STOCK_STATUS_VALUES = Object.values(STOCK_STATUS);

/**
 * Stock status type
 */
export type StockStatus = typeof STOCK_STATUS[keyof typeof STOCK_STATUS];

// ============================================================================
// Purchase Order Status Enums
// ============================================================================

/**
 * Purchase order status enumeration
 */
export const PURCHASE_ORDER_STATUS = {
  DRAFT: 'draft',
  PENDING_APPROVAL: 'pending_approval',
  APPROVED: 'approved',
  ORDERED: 'ordered',
  PARTIALLY_RECEIVED: 'partially_received',
  FULLY_RECEIVED: 'fully_received',
  CANCELLED: 'cancelled'
} as const;

/**
 * Array of purchase order status values for validation
 */
export const PURCHASE_ORDER_STATUS_VALUES = Object.values(PURCHASE_ORDER_STATUS);

/**
 * Purchase order status type
 */
export type PurchaseOrderStatus = typeof PURCHASE_ORDER_STATUS[keyof typeof PURCHASE_ORDER_STATUS];

// ============================================================================
// Sales Order Status Enums
// ============================================================================

/**
 * Sales order status enumeration
 */
export const SALES_ORDER_STATUS = {
  DRAFT: 'draft',
  PENDING_APPROVAL: 'pending_approval',
  APPROVED: 'approved',
  PROCESSING: 'processing',
  PARTIALLY_SHIPPED: 'partially_shipped',
  FULLY_SHIPPED: 'fully_shipped',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  ON_HOLD: 'on_hold'
} as const;

/**
 * Array of sales order status values for validation
 */
export const SALES_ORDER_STATUS_VALUES = Object.values(SALES_ORDER_STATUS);

/**
 * Sales order status type
 */
export type SalesOrderStatus = typeof SALES_ORDER_STATUS[keyof typeof SALES_ORDER_STATUS];

// ============================================================================
// Shipment Status Enums
// ============================================================================

/**
 * Shipment status enumeration
 */
export const SHIPMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  IN_TRANSIT: 'in_transit',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  FAILED_DELIVERY: 'failed_delivery'
} as const;

/**
 * Array of shipment status values for validation
 */
export const SHIPMENT_STATUS_VALUES = Object.values(SHIPMENT_STATUS);

/**
 * Shipment status type
 */
export type ShipmentStatus = typeof SHIPMENT_STATUS[keyof typeof SHIPMENT_STATUS];

// ============================================================================
// Quality Check Status Enums
// ============================================================================

/**
 * Quality check status enumeration
 */
export const QUALITY_CHECK_STATUS = {
  PENDING: 'pending',
  PASS: 'pass',
  FAIL: 'fail',
  REWORK_REQUIRED: 'rework_required',
  REWORKED_PASS: 'reworked_pass',
  REWORKED_FAIL: 'reworked_fail'
} as const;

/**
 * Array of quality check status values for validation
 */
export const QUALITY_CHECK_STATUS_VALUES = Object.values(QUALITY_CHECK_STATUS);

/**
 * Quality check status type
 */
export type QualityCheckStatus = typeof QUALITY_CHECK_STATUS[keyof typeof QUALITY_CHECK_STATUS];

// ============================================================================
// User and Account Status Enums
// ============================================================================

/**
 * User account status enumeration
 */
export const ACCOUNT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ON_HOLD: 'on_hold',
  SUSPENDED: 'suspended',
  PENDING: 'pending'
} as const;

/**
 * Array of account status values for validation
 */
export const ACCOUNT_STATUS_VALUES = Object.values(ACCOUNT_STATUS);

/**
 * Account status type
 */
export type AccountStatus = typeof ACCOUNT_STATUS[keyof typeof ACCOUNT_STATUS];

// ============================================================================
// Unit of Measure Enums
// ============================================================================

/**
 * Unit of measure enumeration
 */
export const UNIT_OF_MEASURE = {
  EACH: 'ea',
  FEET: 'ft',
  METER: 'm',
  KILOGRAM: 'kg',
  GRAM: 'g',
  POUND: 'lb',
  OUNCE: 'oz',
  LITER: 'l',
  MILLILITER: 'ml',
  MILLIMETER: 'mm',
  CENTIMETER: 'cm',
  INCH: 'in'
} as const;

/**
 * Array of unit of measure values for validation
 */
export const UNIT_OF_MEASURE_VALUES = Object.values(UNIT_OF_MEASURE);

/**
 * Unit of measure type
 */
export type UnitOfMeasure = typeof UNIT_OF_MEASURE[keyof typeof UNIT_OF_MEASURE];

// ============================================================================
// Item Type Enums
// ============================================================================

/**
 * Item type enumeration for inventory tracking
 */
export const ITEM_TYPE = {
  PART: 'Part',
  ASSEMBLY: 'Assembly',
  PRODUCT: 'Product'
} as const;

/**
 * Array of item type values for validation
 */
export const ITEM_TYPE_VALUES = Object.values(ITEM_TYPE);

/**
 * Item type
 */
export type ItemType = typeof ITEM_TYPE[keyof typeof ITEM_TYPE];

// ============================================================================
// Transaction Type Enums
// ============================================================================

/**
 * Inventory transaction type enumeration
 */
export const TRANSACTION_TYPE = {
  STOCK_IN_PURCHASE: 'stock_in_purchase',
  STOCK_OUT_PRODUCTION: 'stock_out_production',
  ADJUSTMENT_CYCLE_COUNT: 'adjustment_cycle_count',
  STOCK_IN_PRODUCTION: 'stock_in_production',
  TRANSFER_OUT: 'transfer_out',
  TRANSFER_IN: 'transfer_in',
  SALES_SHIPMENT: 'sales_shipment',
  RECEIPT: 'receipt',
  ISSUE: 'issue',
  ADJUSTMENT: 'adjustment',
  TRANSFER: 'transfer',
  RETURN: 'return'
} as const;

/**
 * Array of transaction type values for validation
 */
export const TRANSACTION_TYPE_VALUES = Object.values(TRANSACTION_TYPE);

/**
 * Transaction type
 */
export type TransactionType = typeof TRANSACTION_TYPE[keyof typeof TRANSACTION_TYPE];

// ============================================================================
// Reference Type Enums
// ============================================================================

/**
 * Reference type enumeration for transactions and quality checks
 */
export const REFERENCE_TYPE = {
  PURCHASE_ORDER: 'PurchaseOrder',
  WORK_ORDER: 'WorkOrder',
  SALES_ORDER: 'SalesOrder',
  STOCK_ADJUSTMENT: 'StockAdjustment',
  PURCHASE_ORDER_RECEIPT: 'PurchaseOrderReceipt'
} as const;

/**
 * Array of reference type values for validation
 */
export const REFERENCE_TYPE_VALUES = Object.values(REFERENCE_TYPE);

/**
 * Reference type
 */
export type ReferenceType = typeof REFERENCE_TYPE[keyof typeof REFERENCE_TYPE];

import React from 'react';
import dynamic from 'next/dynamic';
import { FooterProps } from './types';

// Use dynamic import to load the client component with SSR disabled
const FooterClient = dynamic(() => import('./FooterClient'), {
  ssr: false
});

/**
 * Footer component for the application
 * Server component that delegates rendering to the client component
 * 
 * @param props - Props to pass to the client component
 */
const Footer: React.FC<FooterProps> = (props) => {
  return <FooterClient {...props} />;
};

export default Footer; 
import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"

/**
 * Props for the Select component
 * Includes all native SelectPrimitive.Root props
 */
export type SelectProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root>

/**
 * Props for the SelectTrigger component
 */
export type SelectTriggerProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>

/**
 * Props for the SelectContent component
 */
export type SelectContentProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>

/**
 * Props for the SelectItem component
 */
export type SelectItemProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>

/**
 * Props for the SelectLabel component
 */
export type SelectLabelProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>

/**
 * Props for the SelectSeparator component
 */
export type SelectSeparatorProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>

/**
 * Props for the SelectScrollUpButton component
 */
export type SelectScrollUpButtonProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>

/**
 * Props for the SelectScrollDownButton component
 */
export type SelectScrollDownButtonProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>

/**
 * Re-export types from SelectPrimitive for convenience
 */
export type {
  SelectGroup,
  SelectValue,
} from "@radix-ui/react-select" 
/**
 * Shared Form Components - Optimized Barrel Exports
 * 
 * This file provides optimized exports for shared form components
 * to improve tree-shaking and reduce bundle size.
 */

// Core shared form components (optimized with React.memo)
export { 
  BasicInformationSection,
  CategorySelectionField,
  PriceField,
  StatusSelectionField 
} from './StandardizedFormFields';

// Export types for better TypeScript support
export type {
  BasicInformationSectionProps,
  CategorySelectionFieldProps,
  PriceFieldProps,
  StatusSelectionFieldProps
} from './StandardizedFormFields';

// Re-export commonly used form utilities
export { cn } from '@/app/lib/utils';

// Common status options for reuse across components
export const COMMON_STATUS_OPTIONS = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'draft', label: 'Draft' },
  { value: 'archived', label: 'Archived' }
] as const;

// Common entity types for form configuration
export const ENTITY_TYPES = {
  PRODUCT: 'product',
  ASSEMBLY: 'assembly'
} as const;

export type EntityType = typeof ENTITY_TYPES[keyof typeof ENTITY_TYPES];

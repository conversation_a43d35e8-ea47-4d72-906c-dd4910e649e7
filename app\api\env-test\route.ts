import { NextRequest, NextResponse } from 'next/server';
import { env } from '../../utils/env';

/**
 * Simple environment variable test endpoint
 * This endpoint shows exactly what environment variables are available in Vercel
 */
export async function GET(request: NextRequest) {
  const envData = {
    timestamp: new Date().toISOString(),
    nodeEnv: env.NODE_ENV,
    platform: typeof process !== 'undefined' ? process.platform : 'unknown',

    // MongoDB environment variables
    mongodb: {
      MONGODB_URI: env.MONGODB_URI ? 'SET' : 'NOT SET',
      MONGODB_URI_PROD: env.MONGODB_URI_PROD ? 'SET' : 'NOT SET',
      MONGODB_URI_DEV: env.MONGODB_URI_DEV ? 'SET' : 'NOT SET',
      MONGODB_DB_NAME: env.MONGODB_DB_NAME || 'NOT SET',
    },
    
    // Show which URI would be selected based on current logic
    selectedUri: (() => {
      if (env.NODE_ENV === 'production') {
        if (env.MONGODB_URI_PROD) return 'MONGODB_URI_PROD';
        if (env.MONGODB_URI) return 'MONGODB_URI (fallback)';
        return 'NONE';
      } else {
        if (env.MONGODB_URI_DEV) return 'MONGODB_URI_DEV';
        if (env.MONGODB_URI) return 'MONGODB_URI (fallback)';
        return 'NONE';
      }
    })(),

    // All environment variables containing 'MONGODB'
    allMongodbVars: typeof process !== 'undefined'
      ? Object.keys(process.env).filter(key => key.includes('MONGODB'))
      : [],

    // Show first few characters of URI for verification (if set)
    uriPreview: (() => {
      let uri: string | undefined;
      if (env.NODE_ENV === 'production') {
        uri = env.MONGODB_URI_PROD || env.MONGODB_URI;
      } else {
        uri = env.MONGODB_URI_DEV || env.MONGODB_URI;
      }
      return uri ? `${uri.substring(0, 20)}...` : 'NOT SET';
    })(),
  };

  return NextResponse.json(envData, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}

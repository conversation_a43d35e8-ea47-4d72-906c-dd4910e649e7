// Runtime configuration for the application
// This allows environment variables to be set at runtime via browser

// Initialize global ENV object if it doesn't exist
window.ENV = window.ENV || {};

// Function to check environment configuration
window.checkEnv = function() {
  const hasMongoDB = Boolean(window.ENV.MONGODB_URI);
  const hasDBName = Boolean(window.ENV.MONGODB_DB_NAME);
  return {
    hasMongoDB,
    hasDBName,
    isConfigured: hasMongoDB && hasDBName
  };
};

// Try to load environment variables from localStorage if available
(function loadEnvFromStorage() {
  try {
    const savedConfig = localStorage.getItem('env_config');
    if (savedConfig) {
      const config = JSON.parse(savedConfig);
      if (config.MONGODB_URI) window.ENV.MONGODB_URI = config.MONGODB_URI;
      if (config.MONGODB_DB_NAME) window.ENV.MONGODB_DB_NAME = config.MONGODB_DB_NAME;
      console.log('Loaded environment configuration from localStorage');
    }
  } catch (error) {
    console.error('Error loading environment configuration from localStorage:', error);
  }
})();
/**
 * Order Types for Trend IMS
 * 
 * This file contains type definitions for work orders and related order management entities.
 */

// ============================================================================
// Work Order Types
// ============================================================================

/**
 * Work Order status enumeration
 */
export type WorkOrderStatus = 'planned' | 'released' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled' | 'closed';

/**
 * Work Order priority enumeration
 */
export type WorkOrderPriority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Work Order interface based on the workOrder model
 */
export interface WorkOrder {
  _id: string;
  woNumber: string; // Unique business identifier
  assemblyId?: string | null; // Reference to assemblies._id
  partIdToManufacture?: string | null; // Reference to parts._id
  productId?: string | null; // Reference to products._id
  quantity: number;
  status: WorkOrderStatus;
  priority: WorkOrderPriority;
  dueDate: Date;
  startDate?: Date | null;
  assignedTo?: string | null; // Reference to users._id
  notes?: string | null;
  completedAt?: Date | null;
  estimatedDuration?: number | null; // in hours
  actualDuration?: number | null; // in hours
  costEstimate?: number | null;
  actualCost?: number | null;
  createdBy?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Work Order creation request interface
 */
export interface CreateWorkOrderRequest {
  assemblyId?: string;
  partIdToManufacture?: string;
  productId?: string;
  quantity: number;
  priority: WorkOrderPriority;
  dueDate: Date;
  assignedTo?: string;
  notes?: string;
  estimatedDuration?: number;
  costEstimate?: number;
}

/**
 * Work Order update request interface
 */
export interface UpdateWorkOrderRequest {
  assemblyId?: string;
  partIdToManufacture?: string;
  productId?: string;
  quantity?: number;
  status?: WorkOrderStatus;
  priority?: WorkOrderPriority;
  dueDate?: Date;
  startDate?: Date;
  assignedTo?: string;
  notes?: string;
  completedAt?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  costEstimate?: number;
  actualCost?: number;
}

/**
 * Work Order with populated references for display
 */
export interface WorkOrderWithDetails extends WorkOrder {
  assembly?: {
    _id: string;
    assemblyCode: string;
    name: string;
  };
  part?: {
    _id: string;
    partNumber: string;
    name: string;
  };
  product?: {
    _id: string;
    productCode: string;
    name: string;
  };
  assignedUser?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdByUser?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// ============================================================================
// Work Order Operations Types
// ============================================================================

/**
 * Work Order operation interface for tracking manufacturing steps
 */
export interface WorkOrderOperation {
  _id: string;
  workOrderId: string;
  operationNumber: number;
  operationName: string;
  description?: string | null;
  machineId?: string | null;
  operatorId?: string | null;
  estimatedDuration: number; // in minutes
  actualDuration?: number | null; // in minutes
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  startTime?: Date | null;
  endTime?: Date | null;
  notes?: string | null;
  qualityCheckRequired: boolean;
  qualityCheckPassed?: boolean | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Work Order material consumption interface
 */
export interface WorkOrderMaterialConsumption {
  _id: string;
  workOrderId: string;
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  plannedQuantity: number;
  actualQuantity?: number | null;
  unitCost?: number | null;
  totalCost?: number | null;
  warehouseId?: string | null;
  issuedBy?: string | null;
  issuedAt?: Date | null;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Work Order Progress and Tracking Types
// ============================================================================

/**
 * Work Order progress interface
 */
export interface WorkOrderProgress {
  workOrderId: string;
  overallProgress: number; // percentage 0-100
  operationsCompleted: number;
  totalOperations: number;
  materialsIssued: number;
  totalMaterialsRequired: number;
  estimatedCompletionDate?: Date;
  actualStartDate?: Date;
  lastUpdated: Date;
}

/**
 * Work Order time tracking interface
 */
export interface WorkOrderTimeEntry {
  _id: string;
  workOrderId: string;
  operationId?: string | null;
  userId: string;
  startTime: Date;
  endTime?: Date | null;
  duration?: number | null; // in minutes
  description?: string | null;
  billable: boolean;
  approved: boolean;
  approvedBy?: string | null;
  approvedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Work Order Scheduling Types
// ============================================================================

/**
 * Work Order schedule interface
 */
export interface WorkOrderSchedule {
  _id: string;
  workOrderId: string;
  scheduledStartDate: Date;
  scheduledEndDate: Date;
  actualStartDate?: Date | null;
  actualEndDate?: Date | null;
  machineId?: string | null;
  operatorId?: string | null;
  shiftId?: string | null;
  priority: number; // 1-10, higher is more priority
  dependencies?: string[]; // Array of work order IDs that must complete first
  notes?: string | null;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Work Order Quality Types
// ============================================================================

/**
 * Work Order quality check interface
 */
export interface WorkOrderQualityCheck {
  _id: string;
  workOrderId: string;
  operationId?: string | null;
  checkType: 'incoming' | 'in_process' | 'final';
  checklistId?: string | null;
  inspectorId: string;
  inspectionDate: Date;
  passed: boolean;
  defectsFound?: string[] | null;
  correctiveActions?: string | null;
  notes?: string | null;
  attachments?: string[] | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Utility Types for Work Orders
// ============================================================================

/**
 * Work Order search filters
 */
export interface WorkOrderSearchFilters {
  status?: WorkOrderStatus[];
  priority?: WorkOrderPriority[];
  assignedTo?: string[];
  createdBy?: string[];
  assemblyId?: string[];
  productId?: string[];
  dueDateFrom?: Date;
  dueDateTo?: Date;
  createdFrom?: Date;
  createdTo?: Date;
  searchTerm?: string;
}

/**
 * Work Order sort options
 */
export interface WorkOrderSortOptions {
  field: 'woNumber' | 'dueDate' | 'priority' | 'status' | 'createdAt' | 'quantity';
  direction: 'asc' | 'desc';
}

/**
 * Work Order statistics interface
 */
export interface WorkOrderStatistics {
  total: number;
  byStatus: Record<WorkOrderStatus, number>;
  byPriority: Record<WorkOrderPriority, number>;
  overdue: number;
  completedOnTime: number;
  averageCompletionTime: number; // in hours
  totalValue: number;
}

/**
 * Work Order batch operation interface
 */
export interface WorkOrderBatchOperation {
  workOrderIds: string[];
  operation: 'update_status' | 'assign_user' | 'update_priority' | 'delete';
  parameters: {
    status?: WorkOrderStatus;
    assignedTo?: string;
    priority?: WorkOrderPriority;
    notes?: string;
  };
}

// ============================================================================
// Work Order Reporting Types
// ============================================================================

/**
 * Work Order performance metrics
 */
export interface WorkOrderPerformanceMetrics {
  period: {
    start: Date;
    end: Date;
  };
  totalWorkOrders: number;
  completedWorkOrders: number;
  completionRate: number;
  averageLeadTime: number; // in days
  onTimeDeliveryRate: number;
  costVariance: number; // percentage
  qualityRate: number; // percentage of work orders passing quality checks
  utilizationRate: number; // percentage of available time used
}
